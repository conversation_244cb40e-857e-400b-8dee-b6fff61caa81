"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Trash2,
  Send,
  MoreVertical,
  ExternalLink,
  Copy,
  CheckCircle,
  UserX,
} from "lucide-react";
import { toast } from "sonner";
import { useExcludedSources } from "@/hooks/useExcludedSources";

interface NewsActionsProps {
  newsKey: string;
  newsUrl: string;
  newsAuthor: string;
  onDelete: (key: string) => void;
  onSend: () => void;
  isDeleting: boolean;
  isSending: boolean;
}

export function NewsActions({
  newsKey,
  newsUrl,
  newsAuthor,
  onDelete,
  onSend,
  isDeleting,
  isSending,
}: NewsActionsProps) {
  const [copied, setCopied] = useState(false);

  // Excluded sources hook
  const { isProcessing: isExcludingSource, addExcludedSources } =
    useExcludedSources({
      onSuccess: (response, action, sources) => {
        console.log(`Successfully ${action}ed sources:`, sources);
      },
      onError: (error) => {
        console.error("Excluded sources operation failed:", error);
      },
    });

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(newsUrl);
      setCopied(true);
      toast.success("URL copiada para a área de transferência");
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error("Erro ao copiar URL");
    }
  };

  const handleExcludeSource = async () => {
    if (!newsAuthor) {
      toast.error("Autor da notícia não encontrado");
      return;
    }

    try {
      await addExcludedSources([newsAuthor]);
    } catch (error) {
      console.error("Error excluding source:", error);
    }
  };

  return (
    <div className="flex items-center gap-2">
      {/* Botão de Enviar */}
      <Button
        size="sm"
        onClick={onSend}
        disabled={isSending}
        className="bg-blue-600 hover:bg-blue-700 text-white shadow-xs transition-all duration-200 hover:shadow-md"
      >
        {isSending ? (
          <>
            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2" />
            Enviando...
          </>
        ) : (
          <>
            <Send className="h-3 w-3 mr-2" />
            Enviar
          </>
        )}
      </Button>

      {/* Menu de Ações */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className="h-8 w-8 p-0">
            <MoreVertical className="h-3 w-3" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuItem
            onClick={() => window.open(newsUrl, "_blank")}
            className="cursor-pointer"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            Abrir notícia
          </DropdownMenuItem>

          <DropdownMenuItem onClick={handleCopyUrl} className="cursor-pointer">
            {copied ? (
              <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
            ) : (
              <Copy className="h-4 w-4 mr-2" />
            )}
            {copied ? "Copiado!" : "Copiar URL"}
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <DropdownMenuItem
            onClick={handleExcludeSource}
            disabled={isExcludingSource}
            className="cursor-pointer text-orange-600 focus:text-orange-600"
          >
            <UserX className="h-4 w-4 mr-2" />
            {isExcludingSource ? "Excluindo fonte..." : "Excluir fonte"}
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <AlertDialog>
            <AlertDialogTrigger asChild>
              <DropdownMenuItem
                onSelect={(e) => e.preventDefault()}
                className="cursor-pointer text-red-600 focus:text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Excluir
              </DropdownMenuItem>
            </AlertDialogTrigger>

            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
                <AlertDialogDescription>
                  Esta ação não pode ser desfeita. A notícia será
                  permanentemente removida do sistema.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => onDelete(newsKey)}
                  disabled={isDeleting}
                  className="bg-red-600 hover:bg-red-700"
                >
                  {isDeleting ? "Excluindo..." : "Excluir"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
