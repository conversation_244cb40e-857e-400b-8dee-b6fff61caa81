"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Search, Filter, CalendarIcon, X, SlidersHorizontal } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { cn } from "@/lib/utils";

interface SearchFiltersProps {
  onSearch: (query: string) => void;
  onFilterByAuthor: (author: string) => void;
  onFilterByDate: (date: Date | undefined) => void;
  onSortChange: (sort: string) => void;
  onClearFilters: () => void;
  authors: string[];
  activeFilters: {
    search: string;
    author: string;
    date: Date | undefined;
    sort: string;
  };
}

export function SearchFilters({ 
  onSearch, 
  onFilterByAuthor, 
  onFilterByDate, 
  onSortChange, 
  onClearFilters,
  authors,
  activeFilters 
}: SearchFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false);

  const hasActiveFilters = activeFilters.search || activeFilters.author || activeFilters.date;

  const handleAuthorChange = (value: string) => {
    onFilterByAuthor(value === "all" ? "" : value);
  };

  return (
    <div className="space-y-4 bg-white p-4 rounded-lg border shadow-xs">
      {/* Busca Principal */}
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Buscar notícias por título, conteúdo ou autor..."
            value={activeFilters.search}
            onChange={(e) => onSearch(e.target.value)}
            className="pl-10 h-11"
          />
        </div>
        <Button
          variant="outline"
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="h-11 px-3"
        >
          <SlidersHorizontal className="h-4 w-4" />
        </Button>
      </div>

      {/* Filtros Avançados */}
      {showAdvanced && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
          {/* Filtro por Autor */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Autor</label>
            <Select value={activeFilters.author || "all"} onValueChange={handleAuthorChange}>
              <SelectTrigger>
                <SelectValue placeholder="Todos os autores" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os autores</SelectItem>
                {authors.map((author) => (
                  <SelectItem key={author} value={author}>
                    {author}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Filtro por Data */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Data</label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !activeFilters.date && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {activeFilters.date ? (
                    format(activeFilters.date, "PPP", { locale: ptBR })
                  ) : (
                    "Selecionar data"
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={activeFilters.date}
                  onSelect={onFilterByDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Ordenação */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Ordenar por</label>
            <Select value={activeFilters.sort} onValueChange={onSortChange}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date-desc">Mais recentes</SelectItem>
                <SelectItem value="date-asc">Mais antigas</SelectItem>
                <SelectItem value="author">Autor (A-Z)</SelectItem>
                <SelectItem value="title">Título (A-Z)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {/* Filtros Ativos */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 items-center">
          <span className="text-sm text-gray-500">Filtros ativos:</span>
          {activeFilters.search && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Busca: {activeFilters.search}
              <X className="h-3 w-3 cursor-pointer" onClick={() => onSearch("")} />
            </Badge>
          )}
          {activeFilters.author && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Autor: {activeFilters.author}
              <X className="h-3 w-3 cursor-pointer" onClick={() => onFilterByAuthor("")} />
            </Badge>
          )}
          {activeFilters.date && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Data: {format(activeFilters.date, "dd/MM/yyyy")}
              <X className="h-3 w-3 cursor-pointer" onClick={() => onFilterByDate(undefined)} />
            </Badge>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="text-red-600 hover:text-red-700"
          >
            Limpar todos
          </Button>
        </div>
      )}
    </div>
  );
}