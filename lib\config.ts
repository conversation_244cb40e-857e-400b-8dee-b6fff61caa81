/**
 * Application configuration management
 * Centralizes all environment variables and configuration settings
 */

interface AppConfig {
  firebase: {
    databaseUrl: string;
  };
  api: {
    cnewsUrl?: string;
    timeout: number;
    retryAttempts: number;
  };
  app: {
    name: string;
    version: string;
    environment: 'development' | 'production' | 'test';
  };
}

/**
 * Validates that required environment variables are present
 * @throws Error if required variables are missing
 */
function validateEnvironment(): void {
  // Skip validation during build time or in production builds
  if (process.env.NODE_ENV === 'production' && !process.env.NEXT_PUBLIC_FIREBASE_DB_URL) {
    console.warn('Warning: NEXT_PUBLIC_FIREBASE_DB_URL not set, using default value');
    return;
  }

  // Only validate in development or when variables are expected to be present
  if (process.env.NODE_ENV === 'development') {
    const required = ['NEXT_PUBLIC_FIREBASE_DB_URL'];
    const missing = required.filter(key => !process.env[key]);

    if (missing.length > 0) {
      console.warn(
        `Warning: Missing environment variables: ${missing.join(', ')}\n` +
        'Using default values. Please check your .env.local file for production use.'
      );
    }
  }
}

/**
 * Creates and validates application configuration
 * @returns Validated application configuration object
 */
function createConfig(): AppConfig {
  // Validate environment in non-test environments
  if (process.env.NODE_ENV !== 'test') {
    validateEnvironment();
  }

  return {
    firebase: {
      databaseUrl: process.env.NEXT_PUBLIC_FIREBASE_DB_URL || 'https://debug-cnews-default-rtdb.firebaseio.com',
    },
    api: {
      cnewsUrl: process.env.NEXT_PUBLIC_CNEWS_API_URL,
      timeout: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '15000', 10),
      retryAttempts: parseInt(process.env.NEXT_PUBLIC_API_RETRY_ATTEMPTS || '3', 10),
    },
    app: {
      name: 'News Admin System',
      version: process.env.npm_package_version || '1.0.0',
      environment: (process.env.NODE_ENV as AppConfig['app']['environment']) || 'development',
    },
  };
}

/**
 * Application configuration instance
 * Singleton pattern to ensure consistent configuration across the app
 */
export const config = createConfig();

/**
 * Type exports for use in other modules
 */
export type { AppConfig };
