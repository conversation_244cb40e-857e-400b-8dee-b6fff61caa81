{"info": {"name": "CNEWS API Collection", "description": "Coleção completa de endpoints da API CNEWS", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "BASE_URL", "value": "http://localhost:8000", "type": "string"}, {"key": "API_V1", "value": "/api/v1", "type": "string"}, {"key": "FIREBASE_TOKEN", "value": "your_firebase_token_here", "type": "string"}], "item": [{"name": "Health Check", "item": [{"name": "GET Health Status", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{BASE_URL}}/health", "host": ["{{BASE_URL}}"], "path": ["health"]}}}]}, {"name": "Coins", "item": [{"name": "GET Coin Data", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{BASE_URL}}{{API_V1}}/coins/", "host": ["{{BASE_URL}}"], "path": ["{{API_V1}}", "coins", ""]}}}, {"name": "POST Sync Coins", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{FIREBASE_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}{{API_V1}}/coins/sync", "host": ["{{BASE_URL}}"], "path": ["{{API_V1}}", "coins", "sync"]}}}]}, {"name": "Indices", "item": [{"name": "GET Economic Indices", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{BASE_URL}}{{API_V1}}/indices/", "host": ["{{BASE_URL}}"], "path": ["{{API_V1}}", "indices", ""]}}}, {"name": "POST Sync Indices", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{FIREBASE_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}{{API_V1}}/indices/sync", "host": ["{{BASE_URL}}"], "path": ["{{API_V1}}", "indices", "sync"]}}}]}, {"name": "News", "item": [{"name": "GET News from Firebase (Debug)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{BASE_URL}}{{API_V1}}/news/?use_debug=true", "host": ["{{BASE_URL}}"], "path": ["{{API_V1}}", "news", ""], "query": [{"key": "use_debug", "value": "true"}]}}}, {"name": "GET News from Firebase (Production)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{BASE_URL}}{{API_V1}}/news/?use_debug=false", "host": ["{{BASE_URL}}"], "path": ["{{API_V1}}", "news", ""], "query": [{"key": "use_debug", "value": "false"}]}}}, {"name": "POST Search News - Simple", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"search_terms\": [\"mercado imobiliário\"],\n  \"time_period\": \"qdr:w\",\n  \"max_results\": 50\n}"}, "url": {"raw": "{{BASE_URL}}{{API_V1}}/news/search", "host": ["{{BASE_URL}}"], "path": ["{{API_V1}}", "news", "search"]}}}, {"name": "POST Search News - Multiple Terms", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"search_terms\": [\n    \"corinthians\",\n    \"palmeiras\",\n    \"são paulo\",\n    \"santos\"\n  ],\n  \"time_period\": \"qdr:d\",\n  \"max_results\": 80\n}"}, "url": {"raw": "{{BASE_URL}}{{API_V1}}/news/search", "host": ["{{BASE_URL}}"], "path": ["{{API_V1}}", "news", "search"]}}}, {"name": "POST Search News - With Excluded Sources", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"search_terms\": [\"tecnologia\", \"inovação\"],\n  \"time_period\": \"qdr:d\",\n  \"max_results\": 100,\n  \"exclude_sources\": [\n    \"Blog do Torcedor\",\n    \"Portal Não Confiável\"\n  ]\n}"}, "url": {"raw": "{{BASE_URL}}{{API_V1}}/news/search", "host": ["{{BASE_URL}}"], "path": ["{{API_V1}}", "news", "search"]}}}, {"name": "POST Sync News - Debug Firebase", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{FIREBASE_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n  \"search_terms\": [\"corinthians\"],\n  \"time_period\": \"qdr:d\",\n  \"days_filter\": 1,\n  \"use_debug_firebase\": true\n}"}, "url": {"raw": "{{BASE_URL}}{{API_V1}}/news/sync", "host": ["{{BASE_URL}}"], "path": ["{{API_V1}}", "news", "sync"]}}}, {"name": "POST Sync News - Production Firebase", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{FIREBASE_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n  \"search_terms\": [\"economia\", \"mercado financeiro\"],\n  \"time_period\": \"qdr:d\",\n  \"days_filter\": 3,\n  \"use_debug_firebase\": false\n}"}, "url": {"raw": "{{BASE_URL}}{{API_V1}}/news/sync", "host": ["{{BASE_URL}}"], "path": ["{{API_V1}}", "news", "sync"]}}}, {"name": "GET Excluded Sources (Debug)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{BASE_URL}}{{API_V1}}/news/excluded-sources?use_debug=true", "host": ["{{BASE_URL}}"], "path": ["{{API_V1}}", "news", "excluded-sources"], "query": [{"key": "use_debug", "value": "true"}]}}}, {"name": "GET Excluded Sources (Production)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{BASE_URL}}{{API_V1}}/news/excluded-sources?use_debug=false", "host": ["{{BASE_URL}}"], "path": ["{{API_V1}}", "news", "excluded-sources"], "query": [{"key": "use_debug", "value": "false"}]}}}, {"name": "POST Add Excluded Sources", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{FIREBASE_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n  \"sources\": [\n    \"Portal Não Confiável\",\n    \"Blog Duvidoso\",\n    \"Site Fake News\"\n  ],\n  \"action\": \"add\",\n  \"use_debug\": true\n}"}, "url": {"raw": "{{BASE_URL}}{{API_V1}}/news/excluded-sources", "host": ["{{BASE_URL}}"], "path": ["{{API_V1}}", "news", "excluded-sources"]}}}, {"name": "POST Remove Excluded Sources", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{FIREBASE_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n  \"sources\": [\n    \"Portal Não Confiável\",\n    \"Blog Duvidoso\"\n  ],\n  \"action\": \"remove\",\n  \"use_debug\": true\n}"}, "url": {"raw": "{{BASE_URL}}{{API_V1}}/news/excluded-sources", "host": ["{{BASE_URL}}"], "path": ["{{API_V1}}", "news", "excluded-sources"]}}}]}]}